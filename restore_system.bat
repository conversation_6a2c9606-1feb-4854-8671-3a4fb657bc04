@echo off
setlocal enabledelayedexpansion
title System Restore - Minecraft FPS Booster

:: Color codes for better display
for /F %%a in ('echo prompt $E ^| cmd') do set "ESC=%%a"
set "RED=%ESC%[91m"
set "GREEN=%ESC%[92m"
set "YELLOW=%ESC%[93m"
set "BLUE=%ESC%[94m"
set "MAGENTA=%ESC%[95m"
set "CYAN=%ESC%[96m"
set "WHITE=%ESC%[97m"
set "RESET=%ESC%[0m"

echo %CYAN%========================================%RESET%
echo %CYAN%        SYSTEM RESTORE%RESET%
echo %CYAN%========================================%RESET%
echo.

:: Check for administrator rights
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo %RED%ERROR: This script requires administrator privileges!%RESET%
    echo %YELLOW%Please run as administrator.%RESET%
    pause
    exit /b 1
)

echo %GREEN%Administrator privileges detected...%RESET%

:: Check if backup folder exists
if not exist "backup" (
    echo %RED%ERROR: Backup folder not found!%RESET%
    echo %YELLOW%The FPS booster may not have been run yet.%RESET%
    pause
    exit /b 1
)

echo %BLUE%Restoring original system settings...%RESET%
echo.

:: 1. Reset Minecraft process priority
echo %YELLOW%[1/7] Resetting Minecraft process priority...%RESET%
tasklist /FI "IMAGENAME eq Minecraft.Windows.exe" 2>NUL | find /I /N "Minecraft.Windows.exe">NUL
if "%ERRORLEVEL%"=="0" (
    wmic process where "name='Minecraft.Windows.exe'" call setpriority "normal" >nul 2>&1
    if !errorlevel! equ 0 (
        echo %GREEN%    ✓ Minecraft priority reset to NORMAL%RESET%
    ) else (
        echo %RED%    ✗ Error resetting priority%RESET%
    )
) else (
    echo %YELLOW%    - Minecraft not running%RESET%
)

:: 2. Restore power settings
echo %YELLOW%[2/7] Restoring power settings...%RESET%
if exist "backup\power_scheme.txt" (
    for /f "tokens=4" %%a in ('type backup\power_scheme.txt ^| find "GUID"') do (
        set "ORIGINAL_SCHEME=%%a"
        powercfg /setactive !ORIGINAL_SCHEME! >nul 2>&1
        if !errorlevel! equ 0 (
            echo %GREEN%    ✓ Original power scheme restored%RESET%
        ) else (
            echo %RED%    ✗ Error restoring power scheme%RESET%
        )
    )
) else (
    echo %YELLOW%    - No power settings backup found%RESET%
    powercfg /setactive 381b4222-f694-41f0-9685-ff5bb260df2e >nul 2>&1
    echo %GREEN%    ✓ Default power scheme (Balanced) set%RESET%
)

:: 3. Restart stopped services
echo %YELLOW%[3/7] Restarting stopped services...%RESET%

:: Restart Windows Search
sc start "WSearch" >nul 2>&1
if !errorlevel! equ 0 (
    echo %GREEN%    ✓ Windows Search started%RESET%
) else (
    echo %YELLOW%    - Windows Search already started or not available%RESET%
)

:: Restart Themes service
sc start "Themes" >nul 2>&1
if !errorlevel! equ 0 (
    echo %GREEN%    ✓ Themes service started%RESET%
) else (
    echo %YELLOW%    - Themes service already started%RESET%
)

:: Restart Fax service (if available)
sc start "Fax" >nul 2>&1
if !errorlevel! equ 0 (
    echo %GREEN%    ✓ Fax service started%RESET%
) else (
    echo %YELLOW%    - Fax service not available%RESET%
)

:: 4. Reset GPU settings
echo %YELLOW%[4/7] Resetting GPU settings...%RESET%
reg add "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\GraphicsDrivers" /v "TdrLevel" /t REG_DWORD /d 2 /f >nul 2>&1
echo %GREEN%    ✓ GPU timeout reset to default%RESET%

:: 5. Reset network settings
echo %YELLOW%[5/7] Resetting network settings...%RESET%
netsh int tcp set global autotuninglevel=normal >nul 2>&1
netsh int tcp set global chimney=disabled >nul 2>&1
netsh int tcp set global rss=disabled >nul 2>&1
echo %GREEN%    ✓ Network settings reset to default%RESET%

:: 6. Reset CPU affinity
echo %YELLOW%[6/7] Resetting CPU affinity...%RESET%
tasklist /FI "IMAGENAME eq Minecraft.Windows.exe" 2>NUL | find /I /N "Minecraft.Windows.exe">NUL
if "%ERRORLEVEL%"=="0" (
    for /f "tokens=2" %%i in ('wmic process where "name='Minecraft.Windows.exe'" get ProcessId /format:value ^| find "ProcessId"') do (
        set "PID=%%i"
        powershell -Command "Get-Process -Id !PID! | ForEach-Object { $_.ProcessorAffinity = [System.IntPtr]::new(0xFF) }" >nul 2>&1
        echo %GREEN%    ✓ CPU affinity reset to all cores%RESET%
    )
) else (
    echo %YELLOW%    - Minecraft not running%RESET%
)

:: 7. Clean up backup files
echo %YELLOW%[7/7] Cleaning up backup files...%RESET%
if exist "backup" (
    rmdir /s /q "backup" >nul 2>&1
    echo %GREEN%    ✓ Backup files deleted%RESET%
) else (
    echo %YELLOW%    - No backup files found%RESET%
)

echo.
echo %GREEN%========================================%RESET%
echo %GREEN%      RESTORE COMPLETED!%RESET%
echo %GREEN%========================================%RESET%
echo.
echo %CYAN%Restored settings:%RESET%
echo %WHITE%• Minecraft process priority to Normal%RESET%
echo %WHITE%• Original power scheme%RESET%
echo %WHITE%• All services restarted%RESET%
echo %WHITE%• GPU settings reset%RESET%
echo %WHITE%• Network settings reset%RESET%
echo %WHITE%• CPU affinity reset%RESET%
echo %WHITE%• Backup files deleted%RESET%
echo.
echo %CYAN%Your system is back to its original state!%RESET%
echo.
pause
