@echo off
setlocal enabledelayedexpansion
title System Wiederherstellung - Minecraft FPS Booster

:: Farbcodes für bessere Darstellung
for /F %%a in ('echo prompt $E ^| cmd') do set "ESC=%%a"
set "RED=%ESC%[91m"
set "GREEN=%ESC%[92m"
set "YELLOW=%ESC%[93m"
set "BLUE=%ESC%[94m"
set "MAGENTA=%ESC%[95m"
set "CYAN=%ESC%[96m"
set "WHITE=%ESC%[97m"
set "RESET=%ESC%[0m"

echo %CYAN%========================================%RESET%
echo %CYAN%    SYSTEM WIEDERHERSTELLUNG%RESET%
echo %CYAN%========================================%RESET%
echo.

:: Administrator-<PERSON><PERSON><PERSON> prüfen
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo %RED%FEHLER: Dieses Skript benötigt Administrator-Rechte!%RESET%
    echo %YELLOW%Bitte führen Sie es als Administrator aus.%RESET%
    pause
    exit /b 1
)

echo %GREEN%Administrator-Rechte erkannt...%RESET%

:: Prüfen ob Backup-Ordner existiert
if not exist "backup" (
    echo %RED%FEHLER: Backup-Ordner nicht gefunden!%RESET%
    echo %YELLOW%Möglicherweise wurde der FPS-Booster noch nicht ausgeführt.%RESET%
    pause
    exit /b 1
)

echo %BLUE%Stelle ursprüngliche Systemeinstellungen wieder her...%RESET%
echo.

:: 1. Minecraft Prozesspriorität zurücksetzen
echo %YELLOW%[1/7] Setze Minecraft Prozesspriorität zurück...%RESET%
tasklist /FI "IMAGENAME eq Minecraft.Windows.exe" 2>NUL | find /I /N "Minecraft.Windows.exe">NUL
if "%ERRORLEVEL%"=="0" (
    wmic process where "name='Minecraft.Windows.exe'" call setpriority "normal" >nul 2>&1
    if !errorlevel! equ 0 (
        echo %GREEN%    ✓ Minecraft Priorität auf NORMAL zurückgesetzt%RESET%
    ) else (
        echo %RED%    ✗ Fehler beim Zurücksetzen der Priorität%RESET%
    )
) else (
    echo %YELLOW%    - Minecraft läuft nicht%RESET%
)

:: 2. Energieeinstellungen wiederherstellen
echo %YELLOW%[2/7] Stelle Energieeinstellungen wieder her...%RESET%
if exist "backup\power_scheme.txt" (
    for /f "tokens=4" %%a in ('type backup\power_scheme.txt ^| find "GUID"') do (
        set "ORIGINAL_SCHEME=%%a"
        powercfg /setactive !ORIGINAL_SCHEME! >nul 2>&1
        if !errorlevel! equ 0 (
            echo %GREEN%    ✓ Ursprüngliches Energieschema wiederhergestellt%RESET%
        ) else (
            echo %RED%    ✗ Fehler beim Wiederherstellen des Energieschemas%RESET%
        )
    )
) else (
    echo %YELLOW%    - Kein Backup der Energieeinstellungen gefunden%RESET%
    powercfg /setactive 381b4222-f694-41f0-9685-ff5bb260df2e >nul 2>&1
    echo %GREEN%    ✓ Standard-Energieschema (Ausbalanciert) gesetzt%RESET%
)

:: 3. Dienste wieder starten
echo %YELLOW%[3/7] Starte gestoppte Dienste wieder...%RESET%

:: Windows Search wieder starten
sc start "WSearch" >nul 2>&1
if !errorlevel! equ 0 (
    echo %GREEN%    ✓ Windows Search gestartet%RESET%
) else (
    echo %YELLOW%    - Windows Search bereits gestartet oder nicht verfügbar%RESET%
)

:: Themes-Dienst wieder starten
sc start "Themes" >nul 2>&1
if !errorlevel! equ 0 (
    echo %GREEN%    ✓ Themes-Dienst gestartet%RESET%
) else (
    echo %YELLOW%    - Themes-Dienst bereits gestartet%RESET%
)

:: Fax-Dienst wieder starten (falls vorhanden)
sc start "Fax" >nul 2>&1
if !errorlevel! equ 0 (
    echo %GREEN%    ✓ Fax-Dienst gestartet%RESET%
) else (
    echo %YELLOW%    - Fax-Dienst nicht verfügbar%RESET%
)

:: 4. GPU-Einstellungen zurücksetzen
echo %YELLOW%[4/7] Setze GPU-Einstellungen zurück...%RESET%
reg add "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\GraphicsDrivers" /v "TdrLevel" /t REG_DWORD /d 2 /f >nul 2>&1
echo %GREEN%    ✓ GPU-Timeout auf Standard zurückgesetzt%RESET%

:: 5. Netzwerk-Einstellungen zurücksetzen
echo %YELLOW%[5/7] Setze Netzwerk-Einstellungen zurück...%RESET%
netsh int tcp set global autotuninglevel=normal >nul 2>&1
netsh int tcp set global chimney=disabled >nul 2>&1
netsh int tcp set global rss=disabled >nul 2>&1
echo %GREEN%    ✓ Netzwerk-Einstellungen auf Standard zurückgesetzt%RESET%

:: 6. CPU-Affinität zurücksetzen
echo %YELLOW%[6/7] Setze CPU-Affinität zurück...%RESET%
tasklist /FI "IMAGENAME eq Minecraft.Windows.exe" 2>NUL | find /I /N "Minecraft.Windows.exe">NUL
if "%ERRORLEVEL%"=="0" (
    for /f "tokens=2" %%i in ('wmic process where "name='Minecraft.Windows.exe'" get ProcessId /format:value ^| find "ProcessId"') do (
        set "PID=%%i"
        powershell -Command "Get-Process -Id !PID! | ForEach-Object { $_.ProcessorAffinity = [System.IntPtr]::new(0xFF) }" >nul 2>&1
        echo %GREEN%    ✓ CPU-Affinität auf alle Kerne zurückgesetzt%RESET%
    )
) else (
    echo %YELLOW%    - Minecraft läuft nicht%RESET%
)

:: 7. Backup-Dateien aufräumen
echo %YELLOW%[7/7] Räume Backup-Dateien auf...%RESET%
if exist "backup" (
    rmdir /s /q "backup" >nul 2>&1
    echo %GREEN%    ✓ Backup-Dateien gelöscht%RESET%
) else (
    echo %YELLOW%    - Keine Backup-Dateien gefunden%RESET%
)

echo.
echo %GREEN%========================================%RESET%
echo %GREEN%    WIEDERHERSTELLUNG ABGESCHLOSSEN!%RESET%
echo %GREEN%========================================%RESET%
echo.
echo %CYAN%Wiederhergestellte Einstellungen:%RESET%
echo %WHITE%• Minecraft Prozesspriorität auf Normal%RESET%
echo %WHITE%• Ursprüngliches Energieschema%RESET%
echo %WHITE%• Alle Dienste wieder gestartet%RESET%
echo %WHITE%• GPU-Einstellungen zurückgesetzt%RESET%
echo %WHITE%• Netzwerk-Einstellungen zurückgesetzt%RESET%
echo %WHITE%• CPU-Affinität zurückgesetzt%RESET%
echo %WHITE%• Backup-Dateien gelöscht%RESET%
echo.
echo %CYAN%Ihr System ist wieder im ursprünglichen Zustand!%RESET%
echo.
pause
