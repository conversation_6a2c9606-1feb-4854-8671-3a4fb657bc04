@echo off
setlocal enabledelayedexpansion
title Minecraft FPS Booster v1.0

:: Farbcodes für bessere Darstellung
for /F %%a in ('echo prompt $E ^| cmd') do set "ESC=%%a"
set "RED=%ESC%[91m"
set "GREEN=%ESC%[92m"
set "YELLOW=%ESC%[93m"
set "BLUE=%ESC%[94m"
set "MAGENTA=%ESC%[95m"
set "CYAN=%ESC%[96m"
set "WHITE=%ESC%[97m"
set "RESET=%ESC%[0m"

echo %CYAN%========================================%RESET%
echo %CYAN%    MINECRAFT FPS BOOSTER v1.0%RESET%
echo %CYAN%========================================%RESET%
echo.

:: Administrator-<PERSON>chte prüfen
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo %RED%FEHLER: Dieses Skript benötigt Administrator-Rechte!%RESET%
    echo %YELLOW%Bitte führen Sie es als Administrator aus.%RESET%
    pause
    exit /b 1
)

echo %GREEN%Administrator-Rechte erkannt...%RESET%

:: Prüfen ob Minecraft läuft
tasklist /FI "IMAGENAME eq Minecraft.Windows.exe" 2>NUL | find /I /N "Minecraft.Windows.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo %GREEN%Minecraft.Windows.exe gefunden!%RESET%
    set "MINECRAFT_RUNNING=1"
) else (
    echo %YELLOW%Minecraft.Windows.exe läuft nicht. Booster wird trotzdem angewendet.%RESET%
    set "MINECRAFT_RUNNING=0"
)

echo.
echo %BLUE%Erstelle Backup der aktuellen Einstellungen...%RESET%

:: Backup-Ordner erstellen
if not exist "backup" mkdir backup

:: Aktuelle Energieeinstellungen sichern
powercfg /getactivescheme > backup\power_scheme.txt 2>nul

:: Aktuelle Prozesspriorität sichern (falls Minecraft läuft)
if "%MINECRAFT_RUNNING%"=="1" (
    wmic process where "name='Minecraft.Windows.exe'" get ProcessId,Priority /format:csv > backup\minecraft_priority.txt 2>nul
)

:: Aktuelle Dienste-Status sichern
sc query "Themes" > backup\themes_service.txt 2>nul
sc query "Fax" > backup\fax_service.txt 2>nul
sc query "Spooler" > backup\spooler_service.txt 2>nul

echo %GREEN%Backup erstellt!%RESET%
echo.

echo %BLUE%Starte FPS-Optimierung...%RESET%
echo.

:: 1. Minecraft Prozesspriorität erhöhen
if "%MINECRAFT_RUNNING%"=="1" (
    echo %YELLOW%[1/8] Erhöhe Minecraft Prozesspriorität...%RESET%
    wmic process where "name='Minecraft.Windows.exe'" call setpriority "high priority" >nul 2>&1
    if !errorlevel! equ 0 (
        echo %GREEN%    ✓ Minecraft Priorität auf HOCH gesetzt%RESET%
    ) else (
        echo %RED%    ✗ Fehler beim Setzen der Priorität%RESET%
    )
) else (
    echo %YELLOW%[1/8] Minecraft läuft nicht - Priorität wird gesetzt sobald es startet%RESET%
)

:: 2. Energieeinstellungen auf Höchstleistung
echo %YELLOW%[2/8] Setze Energieeinstellungen auf Höchstleistung...%RESET%
powercfg /setactive 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c >nul 2>&1
if !errorlevel! equ 0 (
    echo %GREEN%    ✓ Höchstleistungs-Modus aktiviert%RESET%
) else (
    echo %RED%    ✗ Fehler beim Aktivieren des Höchstleistungs-Modus%RESET%
)

:: 3. Unnötige Dienste temporär stoppen
echo %YELLOW%[3/8] Stoppe unnötige Dienste...%RESET%

:: Windows Search temporär stoppen
sc stop "WSearch" >nul 2>&1
if !errorlevel! equ 0 (
    echo %GREEN%    ✓ Windows Search gestoppt%RESET%
) else (
    echo %YELLOW%    - Windows Search bereits gestoppt oder nicht verfügbar%RESET%
)

:: Themes-Dienst stoppen (spart GPU-Ressourcen)
sc stop "Themes" >nul 2>&1
if !errorlevel! equ 0 (
    echo %GREEN%    ✓ Themes-Dienst gestoppt%RESET%
) else (
    echo %YELLOW%    - Themes-Dienst bereits gestoppt%RESET%
)

:: Fax-Dienst stoppen (falls vorhanden)
sc stop "Fax" >nul 2>&1
if !errorlevel! equ 0 (
    echo %GREEN%    ✓ Fax-Dienst gestoppt%RESET%
) else (
    echo %YELLOW%    - Fax-Dienst nicht verfügbar%RESET%
)

:: 4. Speicher optimieren
echo %YELLOW%[4/8] Optimiere Arbeitsspeicher...%RESET%
echo. | set /p="    Leere Arbeitsspeicher... "
echo 3 > %SystemRoot%\System32\config\systemprofile\Desktop\empty.txt 2>nul
del %SystemRoot%\System32\config\systemprofile\Desktop\empty.txt 2>nul
echo %GREEN%✓%RESET%

:: 5. Temporäre Dateien löschen
echo %YELLOW%[5/8] Lösche temporäre Dateien...%RESET%
del /q /f /s %TEMP%\*.* >nul 2>&1
echo %GREEN%    ✓ Temporäre Dateien gelöscht%RESET%

:: 6. GPU-Einstellungen optimieren (NVIDIA)
echo %YELLOW%[6/8] Optimiere GPU-Einstellungen...%RESET%
reg add "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\GraphicsDrivers" /v "TdrLevel" /t REG_DWORD /d 0 /f >nul 2>&1
echo %GREEN%    ✓ GPU-Timeout deaktiviert%RESET%

:: 7. Netzwerk-Optimierung für Gaming
echo %YELLOW%[7/8] Optimiere Netzwerk für Gaming...%RESET%
netsh int tcp set global autotuninglevel=normal >nul 2>&1
netsh int tcp set global chimney=enabled >nul 2>&1
netsh int tcp set global rss=enabled >nul 2>&1
echo %GREEN%    ✓ Netzwerk-Einstellungen optimiert%RESET%

:: 8. Prozess-Affinität setzen (falls Minecraft läuft)
echo %YELLOW%[8/8] Setze CPU-Affinität...%RESET%
if "%MINECRAFT_RUNNING%"=="1" (
    for /f "tokens=2" %%i in ('wmic process where "name='Minecraft.Windows.exe'" get ProcessId /format:value ^| find "ProcessId"') do (
        set "PID=%%i"
        powershell -Command "Get-Process -Id !PID! | ForEach-Object { $_.ProcessorAffinity = [System.IntPtr]::new(0xFF) }" >nul 2>&1
        echo %GREEN%    ✓ CPU-Affinität für alle Kerne gesetzt%RESET%
    )
) else (
    echo %YELLOW%    - Minecraft läuft nicht, Affinität wird später gesetzt%RESET%
)

echo.
echo %GREEN%========================================%RESET%
echo %GREEN%    FPS-BOOSTER ERFOLGREICH AKTIVIERT!%RESET%
echo %GREEN%========================================%RESET%
echo.
echo %CYAN%Optimierungen angewendet:%RESET%
echo %WHITE%• Minecraft Prozesspriorität erhöht%RESET%
echo %WHITE%• Energieeinstellungen auf Höchstleistung%RESET%
echo %WHITE%• Unnötige Dienste gestoppt%RESET%
echo %WHITE%• Arbeitsspeicher optimiert%RESET%
echo %WHITE%• Temporäre Dateien gelöscht%RESET%
echo %WHITE%• GPU-Einstellungen optimiert%RESET%
echo %WHITE%• Netzwerk für Gaming optimiert%RESET%
echo %WHITE%• CPU-Affinität gesetzt%RESET%
echo.
echo %YELLOW%WICHTIG: Führen Sie 'restore_system.bat' aus, um alle%RESET%
echo %YELLOW%Änderungen rückgängig zu machen!%RESET%
echo.

:: Kontinuierliche Überwachung anbieten
echo %CYAN%Möchten Sie die kontinuierliche Minecraft-Überwachung starten? (J/N)%RESET%
set /p "choice=Eingabe: "
if /i "%choice%"=="J" (
    echo.
    echo %GREEN%Starte kontinuierliche Überwachung...%RESET%
    echo %YELLOW%Drücken Sie STRG+C zum Beenden%RESET%
    echo.
    
    :monitor_loop
    timeout /t 5 /nobreak >nul
    
    :: Prüfen ob Minecraft noch läuft
    tasklist /FI "IMAGENAME eq Minecraft.Windows.exe" 2>NUL | find /I /N "Minecraft.Windows.exe">NUL
    if "%ERRORLEVEL%"=="0" (
        :: Priorität erneut setzen (falls zurückgesetzt)
        wmic process where "name='Minecraft.Windows.exe'" call setpriority "high priority" >nul 2>&1
        echo %GREEN%[%time%] Minecraft überwacht - Priorität aufrechterhalten%RESET%
    ) else (
        echo %RED%[%time%] Minecraft beendet - Überwachung gestoppt%RESET%
        goto end_monitor
    )
    
    goto monitor_loop
    
    :end_monitor
)

echo.
echo %CYAN%Viel Spaß beim Gaming!%RESET%
pause
