@echo off
setlocal enabledelayedexpansion
title Minecraft FPS Booster v1.0

:: Color codes for better display
for /F %%a in ('echo prompt $E ^| cmd') do set "ESC=%%a"
set "RED=%ESC%[91m"
set "GREEN=%ESC%[92m"
set "YELLOW=%ESC%[93m"
set "BLUE=%ESC%[94m"
set "MAGENTA=%ESC%[95m"
set "CYAN=%ESC%[96m"
set "WHITE=%ESC%[97m"
set "RESET=%ESC%[0m"

echo %CYAN%========================================%RESET%
echo %CYAN%    MINECRAFT FPS BOOSTER v1.0%RESET%
echo %CYAN%========================================%RESET%
echo.

:: Check for administrator rights
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo %RED%ERROR: This script requires administrator privileges!%RESET%
    echo %YELLOW%Please run as administrator.%RESET%
    pause
    exit /b 1
)

echo %GREEN%Administrator privileges detected...%RESET%

:: Check if Minecraft is running
tasklist /FI "IMAGENAME eq Minecraft.Windows.exe" 2>NUL | find /I /N "Minecraft.Windows.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo %GREEN%Minecraft.Windows.exe found!%RESET%
    set "MINECRAFT_RUNNING=1"
) else (
    echo %YELLOW%Minecraft.Windows.exe not running. Booster will still be applied.%RESET%
    set "MINECRAFT_RUNNING=0"
)

echo.
echo %BLUE%Creating backup of current settings...%RESET%

:: Create backup folder
if not exist "backup" mkdir backup

:: Backup current power settings
powercfg /getactivescheme > backup\power_scheme.txt 2>nul

:: Backup current process priority (if Minecraft is running)
if "%MINECRAFT_RUNNING%"=="1" (
    wmic process where "name='Minecraft.Windows.exe'" get ProcessId,Priority /format:csv > backup\minecraft_priority.txt 2>nul
)

:: Backup current service status
sc query "Themes" > backup\themes_service.txt 2>nul
sc query "Fax" > backup\fax_service.txt 2>nul
sc query "Spooler" > backup\spooler_service.txt 2>nul

echo %GREEN%Backup created!%RESET%
echo.

echo %BLUE%Starting FPS optimization...%RESET%
echo.

:: 1. Increase Minecraft process priority
if "%MINECRAFT_RUNNING%"=="1" (
    echo %YELLOW%[1/8] Increasing Minecraft process priority...%RESET%
    wmic process where "name='Minecraft.Windows.exe'" call setpriority "high priority" >nul 2>&1
    if !errorlevel! equ 0 (
        echo %GREEN%    ✓ Minecraft priority set to HIGH%RESET%
    ) else (
        echo %RED%    ✗ Error setting priority%RESET%
    )
) else (
    echo %YELLOW%[1/8] Minecraft not running - priority will be set when it starts%RESET%
)

:: 2. Set power settings to high performance
echo %YELLOW%[2/8] Setting power settings to high performance...%RESET%
powercfg /setactive 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c >nul 2>&1
if !errorlevel! equ 0 (
    echo %GREEN%    ✓ High performance mode activated%RESET%
) else (
    echo %RED%    ✗ Error activating high performance mode%RESET%
)

:: 3. Stop unnecessary services temporarily
echo %YELLOW%[3/8] Stopping unnecessary services...%RESET%

:: Stop Windows Search temporarily
sc stop "WSearch" >nul 2>&1
if !errorlevel! equ 0 (
    echo %GREEN%    ✓ Windows Search stopped%RESET%
) else (
    echo %YELLOW%    - Windows Search already stopped or not available%RESET%
)

:: Stop Themes service (saves GPU resources)
sc stop "Themes" >nul 2>&1
if !errorlevel! equ 0 (
    echo %GREEN%    ✓ Themes service stopped%RESET%
) else (
    echo %YELLOW%    - Themes service already stopped%RESET%
)

:: Stop Fax service (if available)
sc stop "Fax" >nul 2>&1
if !errorlevel! equ 0 (
    echo %GREEN%    ✓ Fax service stopped%RESET%
) else (
    echo %YELLOW%    - Fax service not available%RESET%
)

:: 4. Optimize memory
echo %YELLOW%[4/8] Optimizing memory...%RESET%
echo. | set /p="    Clearing memory... "
echo 3 > %SystemRoot%\System32\config\systemprofile\Desktop\empty.txt 2>nul
del %SystemRoot%\System32\config\systemprofile\Desktop\empty.txt 2>nul
echo %GREEN%✓%RESET%

:: 5. Delete temporary files
echo %YELLOW%[5/8] Deleting temporary files...%RESET%
del /q /f /s %TEMP%\*.* >nul 2>&1
echo %GREEN%    ✓ Temporary files deleted%RESET%

:: 6. Optimize GPU settings
echo %YELLOW%[6/8] Optimizing GPU settings...%RESET%
reg add "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\GraphicsDrivers" /v "TdrLevel" /t REG_DWORD /d 0 /f >nul 2>&1
echo %GREEN%    ✓ GPU timeout disabled%RESET%

:: 7. Network optimization for gaming
echo %YELLOW%[7/8] Optimizing network for gaming...%RESET%
netsh int tcp set global autotuninglevel=normal >nul 2>&1
netsh int tcp set global chimney=enabled >nul 2>&1
netsh int tcp set global rss=enabled >nul 2>&1
echo %GREEN%    ✓ Network settings optimized%RESET%

:: 8. Set process affinity (if Minecraft is running)
echo %YELLOW%[8/8] Setting CPU affinity...%RESET%
if "%MINECRAFT_RUNNING%"=="1" (
    for /f "tokens=2" %%i in ('wmic process where "name='Minecraft.Windows.exe'" get ProcessId /format:value ^| find "ProcessId"') do (
        set "PID=%%i"
        powershell -Command "Get-Process -Id !PID! | ForEach-Object { $_.ProcessorAffinity = [System.IntPtr]::new(0xFF) }" >nul 2>&1
        echo %GREEN%    ✓ CPU affinity set for all cores%RESET%
    )
) else (
    echo %YELLOW%    - Minecraft not running, affinity will be set later%RESET%
)

echo.
echo %GREEN%========================================%RESET%
echo %GREEN%    FPS BOOSTER SUCCESSFULLY ACTIVATED!%RESET%
echo %GREEN%========================================%RESET%
echo.
echo %CYAN%Applied optimizations:%RESET%
echo %WHITE%• Minecraft process priority increased%RESET%
echo %WHITE%• Power settings set to high performance%RESET%
echo %WHITE%• Unnecessary services stopped%RESET%
echo %WHITE%• Memory optimized%RESET%
echo %WHITE%• Temporary files deleted%RESET%
echo %WHITE%• GPU settings optimized%RESET%
echo %WHITE%• Network optimized for gaming%RESET%
echo %WHITE%• CPU affinity set%RESET%
echo.
echo %YELLOW%IMPORTANT: Run 'restore_system.bat' to undo all%RESET%
echo %YELLOW%changes and restore original settings!%RESET%
echo.

:: Offer continuous monitoring
echo %CYAN%Would you like to start continuous Minecraft monitoring? (Y/N)%RESET%
set /p "choice=Input: "
if /i "%choice%"=="Y" (
    echo.
    echo %GREEN%Starting continuous monitoring...%RESET%
    echo %YELLOW%Press CTRL+C to exit%RESET%
    echo.

    :monitor_loop
    timeout /t 5 /nobreak >nul

    :: Check if Minecraft is still running
    tasklist /FI "IMAGENAME eq Minecraft.Windows.exe" 2>NUL | find /I /N "Minecraft.Windows.exe">NUL
    if "%ERRORLEVEL%"=="0" (
        :: Re-set priority (in case it was reset)
        wmic process where "name='Minecraft.Windows.exe'" call setpriority "high priority" >nul 2>&1
        echo %GREEN%[%time%] Minecraft monitored - priority maintained%RESET%
    ) else (
        echo %RED%[%time%] Minecraft closed - monitoring stopped%RESET%
        goto end_monitor
    )

    goto monitor_loop

    :end_monitor
)

echo.
echo %CYAN%Happy gaming!%RESET%
pause
