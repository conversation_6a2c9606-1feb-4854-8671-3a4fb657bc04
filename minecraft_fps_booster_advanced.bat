@echo off
setlocal enabledelayedexpansion
title Minecraft FPS Booster Advanced v1.0

:: Farbcodes für bessere Darstellung
for /F %%a in ('echo prompt $E ^| cmd') do set "ESC=%%a"
set "RED=%ESC%[91m"
set "GREEN=%ESC%[92m"
set "YELLOW=%ESC%[93m"
set "BLUE=%ESC%[94m"
set "MAGENTA=%ESC%[95m"
set "CYAN=%ESC%[96m"
set "WHITE=%ESC%[97m"
set "RESET=%ESC%[0m"

:: Standard-Konfiguration laden
set "MINECRAFT_PROCESS=Minecraft.Windows.exe"
set "PROCESS_PRIORITY=high"
set "ENABLE_CPU_AFFINITY=true"
set "SERVICES_TO_STOP=WSearch,Themes,Fax"
set "POWER_SCHEME=high_performance"
set "ENABLE_POWER_OPTIMIZATION=true"
set "DISABLE_GPU_TIMEOUT=true"
set "ENABLE_NETWORK_OPTIMIZATION=true"
set "CLEAR_TEMP_FILES=true"
set "OPTIMIZE_MEMORY=true"
set "ENABLE_MONITORING=true"
set "MONITORING_INTERVAL=5"
set "CREATE_BACKUP=true"
set "REQUIRE_ADMIN=true"
set "OPTIMIZE_VISUAL_EFFECTS=true"
set "DISABLE_FULLSCREEN_OPTIMIZATION=true"
set "RESTORE_ON_MINECRAFT_CLOSE=true"

:: Konfigurationsdatei laden (falls vorhanden)
if exist "config.txt" (
    echo %BLUE%Lade Konfiguration aus config.txt...%RESET%
    for /f "usebackq tokens=1,2 delims==" %%a in ("config.txt") do (
        if not "%%a"=="" if not "%%a"=="#" (
            set "%%a=%%b"
        )
    )
    echo %GREEN%Konfiguration geladen!%RESET%
    echo.
)

echo %CYAN%========================================%RESET%
echo %CYAN%  MINECRAFT FPS BOOSTER ADVANCED v1.0%RESET%
echo %CYAN%========================================%RESET%
echo.

:: Administrator-Rechte prüfen
if "%REQUIRE_ADMIN%"=="true" (
    net session >nul 2>&1
    if !errorLevel! neq 0 (
        echo %RED%FEHLER: Dieses Skript benötigt Administrator-Rechte!%RESET%
        echo %YELLOW%Bitte führen Sie es als Administrator aus.%RESET%
        pause
        exit /b 1
    )
    echo %GREEN%Administrator-Rechte erkannt...%RESET%
)

:: Prüfen ob Minecraft läuft
tasklist /FI "IMAGENAME eq %MINECRAFT_PROCESS%" 2>NUL | find /I /N "%MINECRAFT_PROCESS%">NUL
if "%ERRORLEVEL%"=="0" (
    echo %GREEN%%MINECRAFT_PROCESS% gefunden!%RESET%
    set "MINECRAFT_RUNNING=1"
) else (
    echo %YELLOW%%MINECRAFT_PROCESS% läuft nicht.%RESET%
    echo %CYAN%Möchten Sie trotzdem fortfahren? (J/N)%RESET%
    set /p "choice=Eingabe: "
    if /i not "!choice!"=="J" (
        echo %RED%Abgebrochen.%RESET%
        pause
        exit /b 0
    )
    set "MINECRAFT_RUNNING=0"
)

echo.

:: Backup erstellen
if "%CREATE_BACKUP%"=="true" (
    echo %BLUE%Erstelle Backup der aktuellen Einstellungen...%RESET%
    if not exist "backup" mkdir backup
    
    :: Energieeinstellungen sichern
    powercfg /getactivescheme > backup\power_scheme.txt 2>nul
    
    :: Prozesspriorität sichern
    if "%MINECRAFT_RUNNING%"=="1" (
        wmic process where "name='%MINECRAFT_PROCESS%'" get ProcessId,Priority /format:csv > backup\minecraft_priority.txt 2>nul
    )
    
    :: Dienste-Status sichern
    for %%s in (%SERVICES_TO_STOP%) do (
        sc query "%%s" > backup\%%s_service.txt 2>nul
    )
    
    echo %GREEN%Backup erstellt!%RESET%
    echo.
)

echo %BLUE%Starte erweiterte FPS-Optimierung...%RESET%
echo.

:: 1. Minecraft Prozesspriorität erhöhen
if "%MINECRAFT_RUNNING%"=="1" (
    echo %YELLOW%[1/10] Erhöhe Minecraft Prozesspriorität auf %PROCESS_PRIORITY%...%RESET%
    wmic process where "name='%MINECRAFT_PROCESS%'" call setpriority "%PROCESS_PRIORITY% priority" >nul 2>&1
    if !errorlevel! equ 0 (
        echo %GREEN%    ✓ Minecraft Priorität auf %PROCESS_PRIORITY% gesetzt%RESET%
    ) else (
        echo %RED%    ✗ Fehler beim Setzen der Priorität%RESET%
    )
) else (
    echo %YELLOW%[1/10] Minecraft läuft nicht - Priorität wird gesetzt sobald es startet%RESET%
)

:: 2. Energieeinstellungen optimieren
if "%ENABLE_POWER_OPTIMIZATION%"=="true" (
    echo %YELLOW%[2/10] Optimiere Energieeinstellungen...%RESET%
    if "%POWER_SCHEME%"=="high_performance" (
        powercfg /setactive 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c >nul 2>&1
    )
    echo %GREEN%    ✓ Energieeinstellungen optimiert%RESET%
) else (
    echo %YELLOW%[2/10] Energieoptimierung übersprungen%RESET%
)

:: 3. Unnötige Dienste stoppen
echo %YELLOW%[3/10] Stoppe unnötige Dienste...%RESET%
for %%s in (%SERVICES_TO_STOP%) do (
    sc stop "%%s" >nul 2>&1
    if !errorlevel! equ 0 (
        echo %GREEN%    ✓ %%s gestoppt%RESET%
    ) else (
        echo %YELLOW%    - %%s bereits gestoppt oder nicht verfügbar%RESET%
    )
)

:: 4. Speicher optimieren
if "%OPTIMIZE_MEMORY%"=="true" (
    echo %YELLOW%[4/10] Optimiere Arbeitsspeicher...%RESET%
    
    :: Standby-Speicher leeren (Windows 10/11)
    powershell -Command "if (Get-Command 'Clear-RecycleBin' -ErrorAction SilentlyContinue) { Clear-RecycleBin -Force }" >nul 2>&1
    
    :: Arbeitsset aller Prozesse trimmen
    powershell -Command "Get-Process | ForEach-Object { try { $_.WorkingSet = $_.WorkingSet } catch {} }" >nul 2>&1
    
    echo %GREEN%    ✓ Arbeitsspeicher optimiert%RESET%
) else (
    echo %YELLOW%[4/10] Speicheroptimierung übersprungen%RESET%
)

:: 5. Temporäre Dateien löschen
if "%CLEAR_TEMP_FILES%"=="true" (
    echo %YELLOW%[5/10] Lösche temporäre Dateien...%RESET%
    del /q /f /s %TEMP%\*.* >nul 2>&1
    del /q /f /s %SystemRoot%\Temp\*.* >nul 2>&1
    echo %GREEN%    ✓ Temporäre Dateien gelöscht%RESET%
) else (
    echo %YELLOW%[5/10] Temp-Dateien löschen übersprungen%RESET%
)

:: 6. GPU-Einstellungen optimieren
if "%DISABLE_GPU_TIMEOUT%"=="true" (
    echo %YELLOW%[6/10] Optimiere GPU-Einstellungen...%RESET%
    reg add "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\GraphicsDrivers" /v "TdrLevel" /t REG_DWORD /d 0 /f >nul 2>&1
    reg add "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\GraphicsDrivers" /v "TdrDelay" /t REG_DWORD /d 60 /f >nul 2>&1
    echo %GREEN%    ✓ GPU-Einstellungen optimiert%RESET%
) else (
    echo %YELLOW%[6/10] GPU-Optimierung übersprungen%RESET%
)

:: 7. Netzwerk optimieren
if "%ENABLE_NETWORK_OPTIMIZATION%"=="true" (
    echo %YELLOW%[7/10] Optimiere Netzwerk für Gaming...%RESET%
    netsh int tcp set global autotuninglevel=normal >nul 2>&1
    netsh int tcp set global chimney=enabled >nul 2>&1
    netsh int tcp set global rss=enabled >nul 2>&1
    netsh int tcp set global netdma=enabled >nul 2>&1
    echo %GREEN%    ✓ Netzwerk-Einstellungen optimiert%RESET%
) else (
    echo %YELLOW%[7/10] Netzwerk-Optimierung übersprungen%RESET%
)

:: 8. Visuelle Effekte optimieren
if "%OPTIMIZE_VISUAL_EFFECTS%"=="true" (
    echo %YELLOW%[8/10] Optimiere visuelle Effekte...%RESET%
    reg add "HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects" /v "VisualFXSetting" /t REG_DWORD /d 2 /f >nul 2>&1
    echo %GREEN%    ✓ Visuelle Effekte für Leistung optimiert%RESET%
) else (
    echo %YELLOW%[8/10] Visuelle Effekte Optimierung übersprungen%RESET%
)

:: 9. Fullscreen-Optimierung deaktivieren
if "%DISABLE_FULLSCREEN_OPTIMIZATION%"=="true" (
    echo %YELLOW%[9/10] Deaktiviere Fullscreen-Optimierung...%RESET%
    reg add "HKEY_CURRENT_USER\System\GameConfigStore" /v "GameDVR_Enabled" /t REG_DWORD /d 0 /f >nul 2>&1
    reg add "HKEY_CURRENT_USER\System\GameConfigStore" /v "GameDVR_FSEBehaviorMode" /t REG_DWORD /d 2 /f >nul 2>&1
    echo %GREEN%    ✓ Fullscreen-Optimierung deaktiviert%RESET%
) else (
    echo %YELLOW%[9/10] Fullscreen-Optimierung Deaktivierung übersprungen%RESET%
)

:: 10. CPU-Affinität setzen
if "%ENABLE_CPU_AFFINITY%"=="true" and "%MINECRAFT_RUNNING%"=="1" (
    echo %YELLOW%[10/10] Setze CPU-Affinität...%RESET%
    for /f "tokens=2" %%i in ('wmic process where "name='%MINECRAFT_PROCESS%'" get ProcessId /format:value ^| find "ProcessId"') do (
        set "PID=%%i"
        powershell -Command "Get-Process -Id !PID! | ForEach-Object { $_.ProcessorAffinity = [System.IntPtr]::new(0xFF) }" >nul 2>&1
        echo %GREEN%    ✓ CPU-Affinität für alle Kerne gesetzt%RESET%
    )
) else (
    echo %YELLOW%[10/10] CPU-Affinität übersprungen%RESET%
)

echo.
echo %GREEN%========================================%RESET%
echo %GREEN%  ERWEITERTE OPTIMIERUNG ABGESCHLOSSEN!%RESET%
echo %GREEN%========================================%RESET%
echo.

:: Überwachung starten
if "%ENABLE_MONITORING%"=="true" (
    echo %CYAN%Starte kontinuierliche Minecraft-Überwachung...%RESET%
    echo %YELLOW%Drücken Sie STRG+C zum Beenden%RESET%
    echo.
    
    :monitor_loop
    timeout /t %MONITORING_INTERVAL% /nobreak >nul
    
    tasklist /FI "IMAGENAME eq %MINECRAFT_PROCESS%" 2>NUL | find /I /N "%MINECRAFT_PROCESS%">NUL
    if "%ERRORLEVEL%"=="0" (
        wmic process where "name='%MINECRAFT_PROCESS%'" call setpriority "%PROCESS_PRIORITY% priority" >nul 2>&1
        echo %GREEN%[%time%] Minecraft überwacht - Optimierungen aufrechterhalten%RESET%
    ) else (
        echo %RED%[%time%] Minecraft beendet%RESET%
        if "%RESTORE_ON_MINECRAFT_CLOSE%"=="true" (
            echo %YELLOW%Starte automatische Wiederherstellung...%RESET%
            call restore_system.bat
        )
        goto end_monitor
    )
    
    goto monitor_loop
    
    :end_monitor
)

echo.
echo %CYAN%FPS-Booster aktiv! Führen Sie 'restore_system.bat' aus zum Wiederherstellen.%RESET%
pause
